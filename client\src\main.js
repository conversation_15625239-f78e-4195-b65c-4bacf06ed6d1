import { createApp } from 'vue'
import './style.css'
import 'leaflet/dist/leaflet.css'
import App from './App.vue'
import PrimeVue from 'primevue/config'
import Aura from '@primeuix/themes/aura'

const app = createApp(App)
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: 'system'
    }
  }
})

import Button from 'primevue/button'
app.component('XButton', Button)

import Checkbox from 'primevue/checkbox'
app.component('XCheckbox', Checkbox)

app.mount('#app')
