{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --host --port 8000", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue src/", "lint:fix": "eslint --ext .js,.vue src/ --fix"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@tailwindcss/vite": "^4.1.6", "@vue-leaflet/vue-leaflet": "^0.10.1", "leaflet": "^1.9.4", "primeicons": "^7.0.0", "primevue": "^4.3.4", "sass-embedded": "^1.88.0", "tailwindcss": "^4.1.6", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.13", "vue-leaflet": "^0.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.24.1", "@types/leaflet": "^1.9.17", "@vitejs/plugin-vue": "^5.2.2", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.22.0", "prettier": "^3.2.5", "vite": "^6.3.1"}}