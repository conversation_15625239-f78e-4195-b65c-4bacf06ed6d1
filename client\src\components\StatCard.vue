<template>
  <div class="stat-card">
    <div class="stat-header">
      <h3>{{ title }}</h3>
      <!-- <div class="info-icon">i</div> -->
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ value }}</div>
      <div class="progress-circle">
        <div class="circle-bg"></div>
        <div class="circle-progress" :style="progressStyle"></div>
      </div>
    </div>
    <div class="stat-footer">
      <span>vs last month</span>
      <span class="change-value">{{ change }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  change: {
    type: String,
    default: '+7%'
  },
  progress: {
    type: Number,
    default: 45, // Default to 45 degrees for backward compatibility
    validator: (value) => value >= 0 && value <= 360 // Ensure value is between 0 and 360 degrees
  }
})

// Computed property to ensure progress is always treated as a number
const rotationDegrees = computed(() => {
  return Number(props.progress) // Ensure it's a number
})

// Computed style for the progress circle
const progressStyle = computed(() => {
  // Get the progress value (0-360)
  const progress = rotationDegrees.value
  const color = '#ff7a45'

  // Convert progress to a percentage (0-100)
  const percentage = (progress / 360) * 100

  // For a circular progress indicator using CSS, we need a different approach
  // We'll use a conic-gradient for more precise control
  // To start from the top, we need to use 'from 0deg' and rotate the element
  return {
    background: `conic-gradient(${color} 0% ${percentage}%, transparent ${percentage}% 100%)`,
    transform: 'rotate(0deg)', // This rotates the element so the gradient starts from the top
    border: 'none' // Remove the border since we're using background gradient
  }
})
</script>

<style scoped>
.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 1rem; */
}

.stat-header h3 {
  color: #666;
  font-weight: 500;
  margin: 0;
  font-size: 1.5rem;
}

.info-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #999;
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  margin-top: -10px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.progress-circle {
  position: relative;
  /* width: 60px;
  height: 60px; */
  width: 160px;
  height: 160px;
  margin-top: -120px;
}

.circle-bg {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.08);
  position: absolute;
  /* Using the same mask as the progress circle */
  -webkit-mask: radial-gradient(transparent 50px, #000 50px);
  mask: radial-gradient(transparent 50px, #000 50px);
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  box-sizing: border-box;
  /* Using a mask to create the donut/ring effect */
  -webkit-mask: radial-gradient(transparent 50px, #000 50px);
  mask: radial-gradient(transparent 50px, #000 50px);
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #999;
}

.change-value {
  color: #4caf50;
  font-weight: 500;
}
</style>
