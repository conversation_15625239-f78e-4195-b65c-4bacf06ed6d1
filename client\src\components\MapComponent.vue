<template>
  <div style="height: 90vh; width: 100%; position: relative">
    <div class="layer-control">
      <div class="p-3 bg-white rounded shadow">
        <!-- Iterate over layers to create checkboxes -->
        <div
          v-for="layer in layers"
          :key="layer.id"
          class="flex items-center gap-2 mb-2"
        >
          <XCheckbox
            v-model="layer.visible"
            :input-id="layer.id"
            binary
            @click="checkboxClicked"
          />
          <label :for="layer.id" class="ml-1">{{ layer.name }}</label>
        </div>
      </div>
    </div>

    <l-map ref="map" v-model:zoom="zoom" :center="center">
      <l-tile-layer
        url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
        layer-type="base"
        name="OpenStreetMap"
      ></l-tile-layer>
      <!-- Iterate over layers to create LGeoJson components -->
      <template v-for="layer in layers" :key="`geojson-${layer.id}`">
        <l-geo-json
          v-if="layer.geojson && layer.visible"
          :geojson="layer.geojson"
          :options="overlayOptions"
          :options-style="styleFunction()"
        />
      </template>
    </l-map>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import 'leaflet/dist/leaflet.css'
import { latLng } from 'leaflet'
import { LMap, LTileLayer, LGeoJson } from '@vue-leaflet/vue-leaflet'

// Import all geojson data from the assets/geojson folder
import bencanaRaw from '../assets/geojson/bencana.geojson?raw'
import bimaRaw from '../assets/geojson/bima.geojson?raw'
import hotspotRaw from '../assets/geojson/hotspot.geojson?raw'
import jalanProvinsiRaw from '../assets/geojson/jalan_provinsi.geojson?raw'
import jembatanRaw from '../assets/geojson/jembatan.geojson?raw'
import kondisiRaw from '../assets/geojson/kondisi.geojson?raw'
import lerengRaw from '../assets/geojson/lereng.geojson?raw'
import lokasiAlatRaw from '../assets/geojson/lokasi_alat.geojson?raw'

const zoom = ref(13) // Ensure zoom is a ref if v-model:zoom is used
const center = latLng(-7.0584606529532055, 109.0550994873047)

const geojsonFileImports = [
  { name: 'Bencana', rawData: bencanaRaw, id: 'bencana' },
  { name: 'Bima', rawData: bimaRaw, id: 'bima' },
  { name: 'Hotspot', rawData: hotspotRaw, id: 'hotspot' },
  { name: 'Jalan Provinsi', rawData: jalanProvinsiRaw, id: 'jalan_provinsi' },
  { name: 'Jembatan', rawData: jembatanRaw, id: 'jembatan' },
  { name: 'Kondisi', rawData: kondisiRaw, id: 'kondisi' },
  { name: 'Lereng', rawData: lerengRaw, id: 'lereng' },
  { name: 'Lokasi Alat', rawData: lokasiAlatRaw, id: 'lokasi_alat' }
]

const layers = ref([])

const onEachFeatureOverlay = (feature, layer) => {
  layer.on('click', () => {})

  let popupContent = ''
  if (feature.properties.popUp) {
    popupContent += feature.properties.popUp
  } else {
    for (let k in feature.properties) {
      let v = feature.properties[k]
      if (v) {
        popupContent += `<b>${k} : </b>${v}<br />`
      }
    }
  }

  if (feature.properties && feature.properties.popupContent) {
    popupContent += feature.properties.popupContent
  }

  layer.bindPopup(popupContent)
}

const overlayOptions = {
  interactive: true,
  onEachFeature: onEachFeatureOverlay
}

const checkboxClicked = (event) => {
  window.scrollTo({
    top: 600,
    behavior: 'smooth'
  })
}

const styleFunction = () => {
  // Style can be customized based on feature properties if needed
  return {
    weight: 2,
    color: '#3388ff',
    opacity: 1,
    fillColor: '#3388ff',
    fillOpacity: 0.2
  }
}

onMounted(() => {
  geojsonFileImports.forEach((fileImport) => {
    try {
      const geojson = JSON.parse(fileImport.rawData)
      layers.value.push({
        id: fileImport.id,
        name: fileImport.name,
        geojson: geojson.length
          ? { type: 'FeatureCollection', features: geojson }
          : geojson, // Handle empty GeoJSON
        visible: ref(false) // Each layer gets its own reactive visibility state
      })
    } catch (error) {
      console.error(`Error parsing GeoJSON for ${fileImport.name}:`, error)
      layers.value.push({
        id: fileImport.id,
        name: `${fileImport.name} (Error loading)`,
        geojson: null,
        visible: ref(false)
      })
    }
  })
})
</script>

<style lang="scss">
.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 100%;
}

.layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000; /* Ensure it's above the map */
  width: 250px;
  /* background-color: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); */
}

/* Added for better layout of checkboxes if XCheckbox doesn't provide flex */
.flex.items-center {
  display: flex;
  align-items: center;
}
.gap-2 {
  gap: 0.5rem; /* 8px */
}
.mb-2 {
  margin-bottom: 0.5rem; /* 8px */
}
.ml-1 {
  margin-left: 0.25rem; /* 4px */
}

.leaflet-popup-content {
  table {
    thead {
      th:first-child {
        width: 70px;
      }
      th {
        background-color: #f8f9fa;
        color: #212529;
        font-weight: bold;
        padding: 5px;
        vertical-align: top;
      }
    }
    tbody {
      tr {
        td:first-child {
          width: 70px;
        }
        td,
        th {
          padding: 5px;
          vertical-align: top;
        }
      }
    }
  }
}
</style>
