<!DOCTYPE html>
<html lang="en">
<head>
  <title>Bina Marga Jawa Tengah</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta content="DPU Bina Marga Jawa Tengah, Webgis Jalan" name="keywords">
  <meta content="DPU Bina Marga Jawa Tengah, Webgis Jalan" name="description">
  <meta name="author" content="Dewaning Software Development © 2020">

  <!-- Favicons -->
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/img-profil.png" rel="icon">
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/img-profil.png" rel="apple-touch-icon">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Krub:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A==" crossorigin=""/>
  <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js" integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA==" crossorigin=""></script>

  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-pegman-master/leaflet-pegman.css" />
  <script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-pegman-master/leaflet-pegman.js"></script>
  <!-- Google Maps Api -->
  <!-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDbCwhTP2mtDKcb2s8A-bzrwMVKGwK-keY&callback=initialize"></script> -->
  <!-- Navbar -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/templates2/css/stylee.css">
  <!-- Navbar CSS Files -->
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/vendor/icofont/icofont.min.css" rel="stylesheet">
  <!-- Template Main CSS File -->
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/style.css" rel="stylesheet">
  <link href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/slider.css" rel="stylesheet">

  <!-- Lokasi -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-location/dist/L.Control.Locate.min.css" />
  <!-- Fullscreen -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-fullscreen/dist/leaflet.fullscreen.css" />
  <!-- Pencarian -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-search-master/src/leaflet-search.css" />
  <!-- Panel Layer -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-panel-layer/src/leaflet-panel-layers.css" />
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-panel-layer/icons.css" />
  <!-- Leaflet Contribution -->
  <link rel="stylesheet" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-attribution/dist/leaflet-control-condended-attribution.css" />
  <script type="text/javascript" src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-attribution/dist/leaflet-control-condended-attribution.js"></script>
  <!-- Ruler -->
  <link rel="stylesheet" type="text/css" href="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-ruler/src/leaflet-ruler.css">
  <script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-ruler/src/leaflet-ruler.js"></script>
  <style>
    body {
      min-height: 100%;
     
    }

    #map {
      border-radius: 0.125em;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      height: 86%;
      width: auto;
      margin-left: 0px;
      border: 2px solid #1978cf;
      box-shadow: 0 0 8px #999;
    }

    .search-tip b {
      color: #fff;
    }

    .NASIONAL.search-tip b,
    .NASIONAL.leaflet-marker-icon {
      background: #f66
    }

    .PROVINSI.search-tip b,
    .PROVINSI.leaflet-marker-icon {
      background: #66f
    }

    .search-tip {
      white-space: nowrap;
    }

    .search-tip b {
      display: inline-block;
      clear: left;
      float: right;
      padding: 0 4px;
      margin-left: 4px;
    }

    .main {
      width: 50%;
      margin: 50px auto;
    }

    .mySlides {
      display: none;
    }

    .leaflet-control-layers-expanded {
      height: 540px;
      overflow-y: scroll;
      overflow-x: hidden;
    }
    
    .info {
      padding: 6px 8px;
      font: 14px/16px Arial, Helvetica, sans-serif;
      background: white;
      background: rgba(255, 255, 255, 0.8);
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
      border-radius: 5px;
    }

    .info h4 {
      margin: 0 0 5px;
      color: #777;
    }

    .legend {
      text-align: left;
      line-height: 18px;
      color: #555;
    }

    .legend i {
      width: 18px;
      height: 18px;
      float: left;
      margin-right: 8px;
      opacity: 0.7;
    }
  </style>

</head>
<body>
    <div class="wrapper d-flex align-items-stretch">
        <!-- Page Content  -->
        <div id="content">
            <!-- ======= Header ======= -->
<header id="header">
  <div style="max-width: 1350px;" class="container d-flex align-items-center">

    <a><img style="width:90%;margin-top:-10px;margin-bottom:-10px;" src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/img-profil.png" alt="" class="img-fluid"></a>
    <h1 class="logo mr-auto"><a href="#">DINAS BINA MARGA PROVINSI JAWA TENGAH</a></h1>
    <!-- Uncomment below if you prefer to use an image logo -->

    <nav class="nav-menu d-none d-lg-block">
      <ul>
        <li class="active"><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/home">HOME</a></li>
        <li><a href="http://jalancantik.dpubinmarcipka.jatengprov.go.id/">JALAN CANTIK</a></li>
        <li class="drop-down"><a href="">KAB/KOT</a>
						<ul style="height: 300px;width: 200px;overflow-y:scroll;overflow-x:hidden;">
							<li><a href="https://dpupr.banjarnegarakab.go.id/">BANJARNEGARA</a></li>
							<li><a href="https://simwaspu.banyumaskab.go.id/">BANYUMAS</a></li>
							<li><a href="https://dpupr.batangkab.go.id/">BATANG</a></li>
							<li><a href="https://www.arcgis.com/apps/Embed/index.html?webmap=cf805b3fc79a416099682be19bd57783&extent=111.1373,-7.2351,111.8556,-6.8841&home=true&zoom=true&scale=true&search=true&searchextent=true&legend=true&basemap_gallery=true&disable_scroll=false&theme=lig">BLORA</a></li>
							<li><a href="http://binamarga.boyolali.go.id/">BOYOLALI</a></li>
							<li><a href="http://dpu.brebeskab.go.id/bimagepat.html">BREBES</a></li>
							<li><a href="http://dpupr.cilacapkab.go.id/">CILACAP</a></li>
							<li><a href="http://simjalan.demakkab.go.id/">DEMAK</a></li>
							<li><a href="https://dpupr.grobogan.go.id/">GROBOGAN</a></li>
							<li><a href="https://dpupr.jepara.go.id/">JEPARA</a></li>
							<li><a href="http://dpupr.karanganyarkab.go.id/">KARANGANYAR</a></li>
							<li><a href="https://dpupr.kebumenkab.go.id/">KEBUMEN</a></li>
							<li><a href="http://www.dpupr.kendalkab.go.id/">KENDAL</a></li>
							<li><a href="http://dpupr.klatenkab.go.id/">KLATEN</a></li>
							<li><a href="http://pupr.kuduskab.go.id/index.php/lapor_frontend">KUDUS</a></li>
							<li><a href="http://dpupr.magelangkab.go.id/">MAGELANG</a></li>
							<li><a href="https://dputr.patikab.go.id/">PATI</a></li>
							<li><a href="https://dputaru.pekalongankab.go.id/">PEKALONGAN</a></li>
							<li><a href="https://dputr.pemalangkab.go.id/">PEMALANG</a></li>
							<li><a href="https://dpupr.purbalinggakab.go.id/">PURBALINGGA</a></li>
							<li><a href="#">PURWOREJO</a></li>
							<li><a href="#">REMBANG</a></li>
							<li><a href="http://pu.semarangkab.go.id/">SEMARANG</a></li>
							<li><a href="http://si-jalan.sragenkab.go.id">SRAGEN</a></li>
							<li><a href="http://pupr.sukoharjokab.go.id/">SUKOHARJO</a></li>
							<li><a href="https://dpu.tegalkab.go.id/">TEGAL</a></li>
							<li><a href="https://dpupr.temanggungkab.go.id/">TEMANGGUNG</a></li>
							<li><a href="http://dpu.wonogirikab.go.id/web">WONOGIRI</a></li>
							<li><a href="https://dpupr.wonosobokab.go.id/pengaduan_masyarakat">WONOSOBO</a></li>
							<li><a href="http://dpupr.magelangkota.go.id/">KOTA MAGELANG</a></li>
							<li><a href="https://simtaru.duamedia.net/profile">KOTA PEKALONGAN</a></li>
							<li><a href="http://dpupr.salatiga.go.id/">KOTA SALATIGA</a></li>
							<li><a href="https://dpu.semarangkota.go.id/">KOTA SEMARANG</a></li>
							<li><a href="http://dpupr.surakarta.go.id/">KOTA SURAKARTA</a></li>
							<li><a href="https://dpupr.tegalkota.go.id/">KOTA TEGAL</a></li>
						</ul>
					</li>
        <li class="drop-down "><a href="">BPJ</a>
          <ul>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjcilacap">BPJ CILACAP</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjtegal">BPJ TEGAL</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjpekalongan">BPJ PEKALONGAN</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjmagelang">BPJ MAGELANG</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjsemarang">BPJ SEMARANG</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjwonosobo">BPJ WONOSOBO</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjpati">BPJ PATI</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjpurwodadi">BPJ PURWODADI</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/bpjsurakarta">BPJ SURAKARTA</a></li>
          </ul>
        </li>
        <li class="drop-down "><a href="">DATA</a>
          <ul>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/perkerasan">PERKERASAN</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/kondisi">KONDISI</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/lebarjalan">LEBAR JALAN</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/seratusmeter">SDI/IRI</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/datatanah">DATA TANAH</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/datapohon">DATA POHON</a></li>
            <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/perancangan">PERANCANGAN</a></li>
            <li><a href="http://sijean.dpubinmarcipka.jatengprov.go.id">SIJEAN</a></li>
			<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/hutan">KAWASAN HUTAN</a></li>
          </ul>
        </li>
        
        <li class="drop-down "><a href="">RAWAN</a>
			<ul>
				<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/kejadianbanjir">KEJADIAN BANJIR</a></li>
				<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/rawanbanjir">BANJIR</a></li>
				<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/rawangempa">GEMPA</a></li>
				<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/rawanerupsi">ERUPSI</a></li>
				<!--<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/rawangerakantanah">GERAKAN TANAH</a></li>-->
				<li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/seismisitas">SEISMISITAS</a></li>
			</ul>
		  </li>

        <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/phjd">PHJD</a></li>
        <li ><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/proyek">PROYEK</a></li>
        <li><a href="https://webgis.dpubinmarcipka.jatengprov.go.id/auth">LOGIN</a></li>

      </ul>
    </nav><!-- .nav-menu -->


  </div>
</header><!-- End Header -->
            <!-- <div class="row"> -->
    <div class="col-sm-12">
        <div class="card">
            <div class="section-title" style="margin-bottom:-50px;">
                <h2>KONDISI JALAN</h2>
            </div>
            <div class="card-body">
                <figure class="highcharts-figure">
                    <div id="containermantap"></div>
                    <p class="highcharts-description">
                    </p>
                </figure>
                
             </div>
        </div>
    </div>

    <div class="col-sm-12 mt-2">
        <div class="card">
            <div class="card-body">

                <div id="map" style="height: 600px;"></div>
            </div>
        </div>
    </div>
<!-- </div> -->
        </div>
    </div>
      <!-- Footer -->
  <div style="z-index: 999;" class="footer">
    <div><a style="color:#fff;" href="#"><strong>Copyright &copy; <script>
            document.write(new Date().getFullYear())
          </script> | </a> </strong> DINAS BINA MARGA PROVINSI JAWA TENGAH</div>
  </div>
  <!-- Akhir Footer -->

  <!-- Navbar JS -->
  <script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/vendor/jquery/jquery.min.js"></script>
  <script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/Bikin/assets/js/main.js"></script>
  <!-- Akhir navbar js -->
  
     <!-- Highcharts -->
   <script src="https://code.highcharts.com/highcharts.js"></script>
  <script src="https://code.highcharts.com/highcharts-3d.js"></script>
  <script src="https://code.highcharts.com/modules/exporting.js"></script>
  <script src="https://code.highcharts.com/modules/export-data.js"></script>
  <script src="https://code.highcharts.com/modules/accessibility.js"></script>
  <!-- Akhir Highcharts -->

  <script>
    Highcharts.chart('containermantap', {
      chart: {
        type: 'pie',
        options3d: {
          enabled: true,
          alpha: 45
        }
      },
      title: {
        text: 'Kondisi Jalan Provinsi Jawa Tengah'
      },
      subtitle: {
        text: 'Grafik berikut menunjukkan Kondisi Mantap dan Tidak Mantap Jalan Provinsi Jawa Tengah. Kondisi Mantap merupakan gabungan dari Kondisi Baik dan Kondisi Sedang. Kondisi Tidak Mantap merupakan gabungan dari Kondisi Rusak Ringan dan Kondisi Rusak.'
      },
      plotOptions: {
        pie: {
          innerSize: 100,
          depth: 45
        }
      },
      series: [{
        name: 'Persentase(%)',
        data: [
          ['Kondisi Mantap 2.185,059 km', 90.86],
          ['Kondisi Tidak Mantap 219.682 km', 9.14]
        ]
      }]
    });
  </script>

  <script>
    var slideIndex = 1;
    showDivs(slideIndex);

    function plusDivs(n) {
      showDivs(slideIndex += n);
    }

    function showDivs(n) {
      var i;
      var x = document.getElementsByClassName("mySlides");
      if (n > x.length) {
        slideIndex = 1
      }
      if (n < 1) {
        slideIndex = x.length
      }
      for (i = 0; i < x.length; i++) {
        x[i].style.display = "none";
      }
      x[slideIndex - 1].style.display = "block";
    }
  </script>    <script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-panel-layer/src/leaflet-panel-layers.js"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/js/leaflet.ajax.js"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/js/Leaflet.GoogleMutant.js"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/hotspot/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/jembatan/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/bima/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/bencana/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/jalanprovinsi"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/pencarian1/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/kondisiPoint/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/lereng/point"></script>
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/api/data/lokasialat/point"></script>

<!-- Lokasi -->
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-location/src/L.Control.Locate.js"></script>
<!-- Fullscreen -->
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-fullscreen/dist/Leaflet.fullscreen.min.js"></script>
<!-- Pencarian -->
<script src="https://webgis.dpubinmarcipka.jatengprov.go.id/assets/leaflet-search-master/src/leaflet-search.js"></script>

<script type="text/javascript">
	var base_url = "https://webgis.dpubinmarcipka.jatengprov.go.id/";

	var map = L.map('map', {
		condensedAttributionControl: false
	}).setView([-7.2990068, 110.0637878], 9);

	var baseMap = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
		attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
	}).addTo(map);

// 	var stadiaOutdoors = L.tileLayer('https://tiles.stadiamaps.com/tiles/outdoors/{z}/{x}/{y}{r}.png', {
// 	maxZoom: 20,
// 	attribution: '&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>, &copy; <a href="https://openmaptiles.org/">OpenMapTiles</a> &copy; <a href="http://openstreetmap.org">OpenStreetMap</a> contributors'
// 	});

	// Lokasi
	lc = L.control.locate({
		strings: {
			title: "Temukan lokasiku !"
		}
	}).addTo(map);
	// Akhir lokasi

	// Fullscreen
	// map.isFullscreen() 
	// map.toggleFullscreen()

	map.on('fullscreenchange', function() {
		if (map.isFullscreen()) {
			console.log('entered fullscreen');
		} else {
			console.log('exited fullscreen');
		}
	});

	map.addControl(new L.Control.Fullscreen({
		title: {
			'false': 'View Fullscreen',
			'true': 'Exit Fullscreen'
		}
	}));
	// Akhir Fullscreen

	// Ruler
	var options = {
		position: 'topleft',
		lengthUnit: {
			display: 'km',
			decimal: 2,
			factor: null,
			label: 'Jarak:'
		},
		angleUnit: {
			display: '&deg;',
			decimal: 2,
			factor: null,
			label: 'Bearing:'
		}
	};
	L.control.ruler(options).addTo(map);
	// Akhir Ruler

	function popUp(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table class="table">';
			html += '<thead class="thead-dark">';
			html += '<tr><th scope="col">Nama Ruas</th>';
			html += '<th scope="col">' + f.properties['Nm_Ruas'] + '</th></tr>';
			html += '</thead><tbody>';
			html += '<tr><th scope="row">Kab/Kot</th>';
			html += '<td>' + f.properties['Kab_Kot'] + '</td></tr>';
			html += '<tr><th scope="row">Kecamatan</th>';
			html += '<td>' + f.properties['Kecamatan'] + '</td></tr>';
			html += '<tr><th scope="row">Desa/Kel</th>';
			html += '<td>' + f.properties['Desa_Kel'] + '</td></tr>';
			html += '<tr><th scope="row">Status</th>';
			html += '<td>' + f.properties['Status'] + '</td></tr>';
			html += '<tr><th scope="row">Provinsi</th>';
			html += '<td>' + f.properties['Propinsi'] + '</td></tr>';
			html += '</tbody>';
			html += '</table>';

			html += "<a href='https://webgis.dpubinmarcipka.jatengprov.go.id/home/<USER>/" + f.properties['OBJECTID_1'] + "' target='_BLANK'>" +
				'<button  class="btn btn-info btn-sm" ><i class="fa fa-info"></i> Detail</button></a>';
			l.bindPopup(html);
		}
	}

	function popUp2(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table class="table">';
			html += '<thead class="thead-dark">';
			html += '<tr><th scope="col">Nama Ruas</th>';
			html += '<th scope="col">' + f.properties['NAMA_RUAS'] + '</th>';
			html += '</thead>';
			html += '<tr><th scope="col">Panjang</th>';
			html += '<th scope="col">' + f.properties['Panjang'] + ' km</th></tr>';
			html += '</table>';
			l.bindPopup(html);
		}
	}
	
	function popUpJalanTol(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table class="table" style="position: relative;height: 300px;overflow: auto;display: block;">';
			html += '<thead class="thead-dark">';
			html += '<tr><th scope="col">RUAS</th>';
			html += '<th scope="col">' + f.properties['RUAS'] + '</th>';
			html += '</thead>';
			html += '<tr><th scope="col">NAMA</th>';
			html += '<th scope="col">' + f.properties['NAMA'] + '</th></tr>';
			html += '<tr><th scope="col">PANJANG</th>';
			html += '<th scope="col">' + f.properties['PANJANG'] + ' km</th></tr>';
			html += '<tr><th scope="col">STA AWAL</th>';
			html += '<th scope="col">' + f.properties['AWAL'] + '</th></tr>';
			html += '<tr><th scope="col">STA AKHIR</th>';
			html += '<th scope="col">' + f.properties['AKHIR'] + '</th></tr>';
			html += '<tr><th scope="col">BUJT</th>';
			html += '<th scope="col">' + f.properties['BUJT'] + '</th></tr>';
			html += '<tr><th scope="col">ID</th>';
			html += '<th scope="col">' + f.properties['ID'] + '</th></tr>';
			html += '<tr><th scope="col">STATUS</th>';
			html += '<th scope="col">' + f.properties['STATUS'] + '</th></tr>';
			html += '<tr><th scope="col">SUMBER</th>';
			html += '<th scope="col">' + f.properties['SUMBER'] + '</th></tr>';
			html += '<tr><th scope="col">TAHUN OPERASI</th>';
			html += '<th scope="col">' + f.properties['TH_OP'] + '</th></tr>';
			html += '<tr><th scope="col">PEMRAKARSA</th>';
			html += '<th scope="col">' + f.properties['PEMRAKARSA'] + '</th></tr>';
			html += '</table>';
			l.bindPopup(html);
		}
	}
	
	function popUp4(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table class="table">';
			html += '<thead class="thead-dark">';
			html += '<tr><th scope="col">Nama Ruas</th>';
			html += '<th scope="col">' + f.properties['NAMA_RUAS'] + '</th>';
			html += '</thead>';
			html += '<tr><th scope="col">Panjang</th>';
			html += '<th scope="col">' + f.properties['Panjang'].toFixed(2) + ' km</th></tr>';
			html += '<tr><th scope="col">Nomor ruas</th>';
			html += '<th scope="col">' + f.properties['KodeNas'] + '</th></tr>';
			html += '</table>';
			l.bindPopup(html);
		}
	}

	function popUp3(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table>';
			html += '<tr>';
			html += '<td>' + f.properties['Name'] + '</td>';
			html += '</tr>';
			html += '</table>';
			l.bindPopup(html);
		}
	}
	
	function popUpKondisi(f, l) {
		var html = '';

		if (f.properties) {
			html += '<table class="table">';
			html += '<thead class="thead-dark">';
			html += '<tr><th scope="col">Nama Ruas</th>';
			html += '<th scope="col">' + f.properties['NAMA_RUAS'] + '</th>';
			html += '</thead>';
			html += '<tr><th scope="col">BPJ</th>';
			html += '<th scope="col">' + f.properties['BPT'] + ' km</th></tr>';
			html += '<tr><th scope="col">Nomor Ruas</th>';
			html += '<th scope="col">' + f.properties['NO_RUAS'] + '</th></tr>';
			html += '<tr><th scope="col">STA</th>';
			html += '<th scope="col">' + f.properties['STA'] + '</th></tr>';
			html += '<tr><th scope="col">Jenis Perkerasan</th>';
			html += '<th scope="col">' + f.properties['Jns_Keras'] + '</th></tr>';
			html += '<tr><th scope="col">Lebar Perkerasan</th>';
			html += '<th scope="col">' + f.properties['Lbr_Keras'] + ' m</th></tr>';
			html += '<tr><th scope="col">Kondisi IRI</th>';
			html += '<th scope="col">' + f.properties['KNDS_IRI'] + '</th></tr>';
			html += '<tr><th scope="col">Kondisi SDI</th>';
			html += '<th scope="col">' + f.properties['KNDS_SDI'] + '</th></tr>';
			html += '</table>';
			l.bindPopup(html);
		}
	}

	// legend

	function iconByName(name) {
		return '<i class="icon" style="background-color:' + name + ';border-radius:50%"></i>';
	}

	function iconByName2(name) {
		return '<i class="icon icon-' + name + '"></i>';
	}

	function featureToMarker(feature, latlng) {
		return L.marker(latlng, {
			icon: L.divIcon({
				className: 'marker-' + feature.properties.amenity,
				html: iconByName(feature.properties.amenity),
				iconUrl: '../images/marker/' + feature.properties.amenity + '.png',
				iconSize: [25, 25],
				iconAnchor: [12, 41],
				popupAnchor: [1, -34],
				shadowSize: [41, 41]
			})
		});
	}

	function groupClick(event) {
		alert("Clicked on marker" + event.layer.id);
	}

	var baseLayers = [{
		group: "Peta",
		collapsed: true,
		layers: [
		    {
				name: "Base Map",
				layer: baseMap
			},
// 			{
// 				name: "Stadia Outdoors",
// 				layer: stadiaOutdoors
// 			}
		]
	}];


	var layersHotspotPoint = L.geoJSON(hotspotPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [30, 30]
				})
			});
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// akhir dari jembatan nasional

	var layersJembatanPoint = L.geoJSON(jembatanPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [30, 30]
				})
			})
			// markers.addLayer(hehe)
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// map.addLayer(markers);

	// akhir dari jembatan provinsi

	// Masbima
	var layersBimaPoint = L.geoJSON(bimaPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [35, 35]
				})
			});
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// akhir dari Masbima

	// Bencana
	var layersBencanaPoint = L.geoJSON(bencanaPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [40, 40]
				})
			});
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// akhir dari Bencana

	// Alat
	var layersAlatPoint = L.geoJSON(alatPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [50, 50]
				})
			});
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// akhir dari Alat
	
	// Kondisi Jalan
	var layersKondisiPoint = L.geoJSON(kondisiPoint, {
		pointToLayer: function(feature, latlng) {
			// console.log(feature)
			return L.marker(latlng, {
				icon: new L.icon({
					iconUrl: feature.properties.icon,
					iconSize: [35, 35]
				})
			});
		},
		onEachFeature: function(feature, layer) {
			if (feature.properties && feature.properties.name) {
				layer.bindPopup(feature.properties.popUp);
			}
		}
	});
	// akhir dari Kondisi Jalan

	// Jalan Provinsi
	
		var myStyle1 = {
			"color": "#0000ff",
			"weight": 4,
			"opacity": 1
		};

		//Akhir Jalan Provinsi

	// Jalan Nasional

	
		var myStyle32 = {
			"color": "#f60404",
			"weight": 4,
			"opacity": 1
		};

		// Akhir Jalan Nasional


	// Jalan Tol
	
		var myStyle30 = {
			"color": "#00ff00",
			"weight": 4,
			"opacity": 1
		};

		// Akhir Jalan Tol


	// Jalan Kabupaten
	
		var myStyle33 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle34 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle35 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle36 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle37 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle38 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle39 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle40 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle41 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle42 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle43 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle44 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle45 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle46 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle47 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle48 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle49 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle50 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle51 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle52 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle53 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle54 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle55 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle56 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle66 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle57 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle58 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle59 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle60 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle61 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle62 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle63 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle64 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle65 = {
			"color": "#000000",
			"weight": 4,
			"opacity": 1
		};

		// Akhir Jalan Kabupaten

	function localData(text, callResponse) {
		//here can use custom criteria or merge data from multiple layers

		callResponse(pencarian1);

		return { //called to stop previous requests on map move
			abort: function() {
				console.log('aborted request:' + text);
			}
		};
	}

	map.addControl(new L.Control.Search({
		sourceData: localData,
		// text:'Color...', 
		markerLocation: true,
		textPlaceholder: 'Cari Jalan/Jembatan',
		zoom: 15,

	}));

	// BPT
	
		var myStyle11 = {
			"color": "#35ca3d",
			"weight": 4,
			"opacity": 1
		};

	
		var myStyle12 = {
			"color": "#400000",
			"weight": 4,
			"opacity": 1
		};

		//Akhir BPT
	var overLayers = [{
			group: "Jalan Provinsi",
			collapsed: true,
			layers: [
				{
			name: "Jalan Provinsi",
			icon: iconByName("#0000ff"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/jalan/jalan_provinsi1.geojson"],
			{onEachFeature:popUp,
				style: myStyle1,
				pointToLayer: featureToMarker 
			}
			).addTo(map)
			},			]
		},
		{
			group: "Jalan Nasional",
			collapsed: true,
			layers: [
				{
			name: "Jalan Nasional",
			icon: iconByName("#f60404"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/jalan/jalan_nasional11.geojson"],
			{onEachFeature:popUp4,
				style: myStyle32,
				pointToLayer: featureToMarker 
			}
			).addTo(map)
			}			]
		},
		{
			group: "Jalan Tol",
			collapsed: true,
			layers: [
				{
			name: "Jalan Tol",
			icon: iconByName("#00ff00"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/jalan/jalan_tol2.geojson"],
			{onEachFeature:popUpJalanTol,
				style: myStyle30,
				pointToLayer: featureToMarker 
			}
			).addTo(map)
		}			]
		},
		{
			group: "Jalan Kabupaten",
			collapsed: true,
			layers: [
				{
			name: "Banjarnegara",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Banjarnegara.geojson"],
			{onEachFeature:popUp2,
				style: myStyle33,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Banyumas",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Banyumas.geojson"],
			{onEachFeature:popUp2,
				style: myStyle34,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Batang",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Batang.geojson"],
			{onEachFeature:popUp2,
				style: myStyle35,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Blora",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Blora.geojson"],
			{onEachFeature:popUp2,
				style: myStyle36,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Boyolali",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Boyolali.geojson"],
			{onEachFeature:popUp2,
				style: myStyle37,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Brebes",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Brebes.geojson"],
			{onEachFeature:popUp2,
				style: myStyle38,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Cilacap",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Cilacap.geojson"],
			{onEachFeature:popUp2,
				style: myStyle39,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Demak",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Demak.geojson"],
			{onEachFeature:popUp2,
				style: myStyle40,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Grobogan",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Grobogan.geojson"],
			{onEachFeature:popUp2,
				style: myStyle41,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Jepara",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Jepara.geojson"],
			{onEachFeature:popUp2,
				style: myStyle42,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Karanganyar",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Karanganyar.geojson"],
			{onEachFeature:popUp2,
				style: myStyle43,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Kebumen",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Kebumen.geojson"],
			{onEachFeature:popUp2,
				style: myStyle44,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Kendal",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Kendal.geojson"],
			{onEachFeature:popUp2,
				style: myStyle45,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Klaten",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Klaten.geojson"],
			{onEachFeature:popUp2,
				style: myStyle46,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Kudus",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Kudus.geojson"],
			{onEachFeature:popUp2,
				style: myStyle47,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Magelang Kab.",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Magelang_Kab.geojson"],
			{onEachFeature:popUp2,
				style: myStyle48,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Magelang Kota",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Magelang_Kota.geojson"],
			{onEachFeature:popUp2,
				style: myStyle49,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Pati",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Pati.geojson"],
			{onEachFeature:popUp2,
				style: myStyle50,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Pekalongan Kota",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Pekalongan_Kota.geojson"],
			{onEachFeature:popUp2,
				style: myStyle51,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Pemalang",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Pemalang.geojson"],
			{onEachFeature:popUp2,
				style: myStyle52,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Purbalingga",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Purbalingga.geojson"],
			{onEachFeature:popUp2,
				style: myStyle53,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Purworejo",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Purworejo.geojson"],
			{onEachFeature:popUp2,
				style: myStyle54,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Rembang",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Rembang.geojson"],
			{onEachFeature:popUp2,
				style: myStyle55,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Salatiga",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Salatiga.geojson"],
			{onEachFeature:popUp2,
				style: myStyle56,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Semarang Kab",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Semarang.geojson"],
			{onEachFeature:popUp2,
				style: myStyle66,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Semarang Kota",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Semarang_Kota.geojson"],
			{onEachFeature:popUp2,
				style: myStyle57,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Sragen",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Sragen.geojson"],
			{onEachFeature:popUp2,
				style: myStyle58,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Sukoharjo",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Sukoharjo.geojson"],
			{onEachFeature:popUp2,
				style: myStyle59,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Surakarta",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Surakarta.geojson"],
			{onEachFeature:popUp2,
				style: myStyle60,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Tegal",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Tegal.geojson"],
			{onEachFeature:popUp2,
				style: myStyle61,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Tegal Kota",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Tegal_Kota.geojson"],
			{onEachFeature:popUp2,
				style: myStyle62,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Temanggung",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Temanggung.geojson"],
			{onEachFeature:popUp2,
				style: myStyle63,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Wonogiri",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Wonogiri.geojson"],
			{onEachFeature:popUp2,
				style: myStyle64,
				pointToLayer: featureToMarker 
			}
			)
		},{
			name: "Wonosobo",
			icon: iconByName("#000000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/geojson_jalankab/Wonosobo.geojson"],
			{onEachFeature:popUp2,
				style: myStyle65,
				pointToLayer: featureToMarker 
			}
			)
		}			]
		},
		{
			name: "Jembatan Nasional",
			icon: iconByName2('jemnas'),
			layer: layersHotspotPoint
		},
		{
			name: "Jembatan Provinsi",
			icon: iconByName2('jemprov'),
			layer: layersJembatanPoint
		},
		{
			name: "Mas Bima",
			icon: iconByName2('masbima'),
			layer: layersBimaPoint
		},
		{
			name: "Rawan Bencana",
			icon: iconByName2('bencana'),
			layer: layersBencanaPoint
		},
		{
			name: "Kondisi Jalan",
			icon: iconByName2('point'),
			layer: layersKondisiPoint
		},
		{
			name: "Sebaran Alat",
			icon: iconByName2('alat'),
			layer: layersAlatPoint
		},
		{
			name: "Kondisi 100m",
			icon: iconByName("#0000ff"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/jalan/100m.geojson"],
			{
				onEachFeature:popUpKondisi,
				style:  function(feature) {
					switch (feature.properties.KNDS_SDI) {
						case 'BAIK': return {color: "#22ff00", weight: 4, opacity: 1};
						case 'SEDANG':   return {color: "#ffff00", weight: 4, opacity: 1};
						case 'RUSAK RINGAN':   return {color: "#ff9500", weight: 4, opacity: 1};
						case 'RUSAK BERAT':   return {color: "#ff0000", weight: 4, opacity: 1};
					}
				},
				pointToLayer: featureToMarker 
			})
		},
		{
			name: "Batas BPJ",
			icon: iconByName("#35ca3d"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/bpt/batas_BPJ.geojson"],
			{onEachFeature:popUp3,
				style: myStyle11,
				pointToLayer: featureToMarker 
			}
			)
			},,{
			name: "Batas Kabupaten",
			icon: iconByName("#400000"),
			layer: new L.GeoJSON.AJAX(["assets/unggah/bpt/Batas_Kabupaten.geojson"],
			{onEachFeature:popUp3,
				style: myStyle12,
				pointToLayer: featureToMarker 
			}
			)
			},	];


	if (document.body.clientWidth <= 767) {
		var isCollapsed = true;
	} else {
		var isCollapsed = false;
	}

	var panelLayers = new L.Control.PanelLayers(baseLayers, overLayers, {
		collapsibleGroups: true,
		collapsed: isCollapsed
		// compact: true
	});

	map.addControl(panelLayers);

	// Awal Pegman
	var pegmanControl = new L.Control.Pegman({
		position: 'bottomleft', // position of control inside the map
		theme: "leaflet-pegman-v3-default", // or "leaflet-pegman-v3-small"
	});
	pegmanControl.addTo(map);
	// Akhir Pegman
</script></body>

</html>